# Task List - SUSE AI Runtime

## Overview
This document tracks all tasks for building the SUSE AIR platform. Tasks are organized by phase and component.

## Phase 0: Setup
### Write Tests for existing code
- [X] Write tests for existing code

## Phase 1: Core MCP Server & Profile Management

This phase focuses on setting up the database and the basic server structure that can handle MCP connections and route them to the correct profile.

- [X] **Data Model:**
  - [X] Design and create database migration for `profiles` table.
  - [X] Design and create database migration for `roles` table.
  - [X] Design and create database migration for `profile_tools` join table (linking profiles to tools with ACLs).
  - [X] Design and create database migration for `role_profiles` join table (linking roles to profiles).
  - [X] Update database queries and models (`sqlc`).
- [X] **MCP Server Foundation:**
  - [X] Implement the standard MCP connection lifecycle (`initialize`, `initialized`, `shutdown`).
  - [X] Implement the Streamable HTTP transport for JSON-RPC messages.
  - [X] Create a **Profile Router** that maps requests from a URL path (e.g., `/{profile_path}`) to the corresponding profile configuration.

## Phase 2: MCP Features & Authorization

This phase implements the core logic for exposing tools and securing the server according to the MCP specification.

- [X] **MCP `tools` Feature Implementation:**
  - [X] Implement the `tools/list` request handler. It must query the DB based on the profile and user's role/ACLs.
  - [X] Implement the `tools/call` request handler. It must perform an ACL check before delegating to the schema executor.
  - [X] Ensure tool definitions, requests, and results conform to the MCP JSON schema.
- [X] **MCP-Compliant Authorization (OAuth 2.1):**
  - [X] Implement `401 Unauthorized` responses with the `WWW-Authenticate` header pointing to resource metadata.
  - [X] Expose OAuth 2.0 Protected Resource Metadata (`/.well-known/oauth-protected-resource`).
  - [X] Implement an Authorization Server that exposes its own metadata (`/.well-known/oauth-authorization-server`) and supports the Authorization Code Grant with PKCE.
  - [ ] Ensure the MCP Server (as a Resource Server) validates access tokens, including the audience (`aud`) claim.

## Phase 3: Integration & Testing

## CLI Implementation
- [X] Implement CLI using `urfave/cli` for `convert`, `execute`, and `serve` commands.
- [X] Make sure all unit tests pass before continue.
- [X] Make sure all integration tests pass before continue.
- [X] Integrate all components for the complete, end-to-end request lifecycle.
- [X] Write integration tests for the full flow: auth -> routing -> ACL check -> execution.
- [ ] Test the server's compliance and functionality using the official **MCP Inspector** tool.

Phase 4: Profile, Role & User Management CLI/TUI

This phase focuses on building a comprehensive command-line interface (CLI) and a Text User Interface (TUI) for managing all aspects of profiles, tools, roles, and users within the SUSE AIR system.

    [X] Data Model Enhancements:

        [X] Review existing profiles, roles, users, profile_tools, role_profiles, and user_roles tables for completeness.

        [X] Update database queries and models (sqlc) to support full CRUD operations on all entities.

    [ ] CLI Commands (urfave/cli):

        [X] Profile Management (sair profile <command>):

            [X] create <name> [description] [path_segment]

            [X] list

            [X] get <name>

            [X] update <name> --new-name <string> --description <string> --path-segment <string>

            [X] delete <name>

        [X] Tool Association (sair tool <command>):

            [X] list <profile_name>

            [X] associate <profile_name> <tool_name> [acl_rules]

            [X] disassociate <profile_name> <tool_name>

        [X] Role Management (sair role <command>):

            [X] create <role_name>

            [X] list

            [X] get <role_name>

            [X] update <role_name> --new-name <string>

            [X] delete <role_name>

            [X] grant-profile-access <role_name> <profile_name>

            [X] revoke-profile-access <role_name> <profile_name>

            [X] list-profiles <role_name> (List profiles a role can access)

        [X] User Management (sair user <command>):

            [X] create <username> [password]

            [X] list

            [X] get <username>

            [X] update <username> --password <new_password>

            [X] delete <username>

            [X] assign-role <username> <role_name>

            [X] remove-role <username> <role_name>

            [X] list-roles <username> (List roles assigned to a user)

    [ ] TUI Implementation (github.com/charmbracelet/bubbletea):

        Note: Implementation will use github.com/charmbracelet/bubbles for components and github.com/charmbracelet/lipgloss for styling.

        [X] Create a new sair tui command to launch the interactive TUI.

        [ ] Design a main menu for navigating between "Profile Management", "Role Management", and "User Management".

        [ ] Profile Management Screens:

            [X] Listing, creating, viewing details, updating, and deleting profiles.

            [] Associating/disassociating tools with profiles.

        [X] Role Management Screens:

            [X] Listing, creating, viewing details, updating, and deleting roles.

            [X] An interactive view to manage which profiles a role can access.

        [ ] User Management Screens:

            [] User creation and listing.

            [ ] Viewing details, updating (password), and deleting users.

            [] Assigning and removing roles from users.

        [ ] Ensure clear, consistent user feedback for all operations (success/failure messages).

    [ ] Testing:

        [] Write unit tests for all CLI command logic.

        [] Write unit tests for all TUI components (model updates, view rendering).

        [ ] Write integration tests verifying end-to-end CRUD functionality for profiles, users, and roles.

        [ ] Write integration tests for all association/disassociation logic (profile_tools, user_roles, role_profiles).