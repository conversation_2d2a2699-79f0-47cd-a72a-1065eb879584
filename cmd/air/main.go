package main

import (
	"log"
	"os"

	"github.com/ravan/suse-air/internal/cmd"

	"github.com/urfave/cli/v2"
)

func main() {
	app := &cli.App{
		Name:  "air",
		Usage: "A command-line tool for managing MCP tools and servers.",
		Action: func(c *cli.Context) error {
			_ = cli.ShowAppHelp(c)
			return nil
		},

		Commands: []*cli.Command{
			{
				Name:  "convert",
				Usage: "Convert an OpenAPI v3 JSON file to MCP tools and store them in the database.",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:     "file",
						Aliases:  []string{"f"},
						Usage:    "Path to the OpenAPI v3 JSON file to convert.",
						Required: true,
					},
					&cli.StringFlag{
						Name:    "db",
						Usage:   "PostgreSQL connection string.",
						Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
						EnvVars: []string{"DATABASE_URL"},
					},
				},
				Action: func(c *cli.Context) error {
					filePath := c.String("file")
					dbConnectionString := c.String("db")

					if err := cmd.RunConversion(dbConnectionString, filePath); err != nil {
						return err
					}
					return nil
				},
			},
			{
				Name:  "execute",
				Usage: "Execute a stored MCP tool.",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:     "tool",
						Aliases:  []string{"t"},
						Usage:    "The name of the MCP tool to execute.",
						Required: true,
					},
					&cli.StringFlag{
						Name:    "args",
						Aliases: []string{"a"},
						Usage:   "A JSON string of arguments for the tool.",
						Value:   "{}",
					},
					&cli.StringFlag{
						Name:     "base-url",
						Aliases:  []string{"b"},
						Usage:    "The base URL of the target API server (e.g., https://api.example.com).",
						Required: true,
					},
					&cli.StringFlag{
						Name:    "db",
						Usage:   "PostgreSQL connection string.",
						Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
						EnvVars: []string{"DATABASE_URL"},
					},
				},
				Action: func(c *cli.Context) error {
					toolName := c.String("tool")
					args := c.String("args")
					baseURL := c.String("base-url")
					dbConnectionString := c.String("db")
					if err := cmd.RunExecution(dbConnectionString, toolName, args, baseURL); err != nil {
						return err
					}
					return nil
				},
			},
			{
				Name:  "serve",
				Usage: "Start the MCP server.",
				Flags: []cli.Flag{
					&cli.IntFlag{
						Name:    "port",
						Aliases: []string{"p"},
						Usage:   "Port for the MCP server to listen on.",
						Value:   8080,
					},
					&cli.StringFlag{
						Name:    "db",
						Usage:   "PostgreSQL connection string.",
						Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
						EnvVars: []string{"DATABASE_URL"},
					},
				},
				Action: func(c *cli.Context) error {
					port := c.Int("port")
					dbConnectionString := c.String("db")
					cmd.RunServer(port, dbConnectionString)
					return nil
				},
			},
			{
				Name:  "profile",
				Usage: "Manage profiles (groups of MCP tools).",
				Subcommands: []*cli.Command{
					{
						Name:  "create",
						Usage: "Create a new profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the profile.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "description",
								Aliases: []string{"d"},
								Usage:   "Description of the profile.",
							},
							&cli.StringFlag{
								Name:     "path-segment",
								Aliases:  []string{"p"},
								Usage:    "Unique URL path segment for the profile.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileCreate(c)
						},
					},
					{
						Name:  "list",
						Usage: "List all profiles.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileList(c)
						},
					},
					{
						Name:  "get",
						Usage: "Get details of a specific profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the profile to retrieve.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileGet(c)
						},
					},
					{
						Name:  "update",
						Usage: "Update an existing profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Current name of the profile to update.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "new-name",
								Usage:   "New name for the profile (optional).",
							},
							&cli.StringFlag{
								Name:    "new-description",
								Usage:   "New description for the profile (optional).",
							},
							&cli.StringFlag{
								Name:    "new-path-segment",
								Usage:   "New path segment for the profile (optional).",
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileUpdate(c)
						},
					},
					{
						Name:  "delete",
						Usage: "Delete a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the profile to delete.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileDelete(c)
						},
					},
				},
			},
			{
				Name:  "tool",
				Usage: "Manage tool associations with profiles.",
				Subcommands: []*cli.Command{
					{
						Name:  "associate",
						Usage: "Associate an MCP tool with a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "profile",
								Aliases:  []string{"p"},
								Usage:    "Name of the profile to associate the tool with.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "tool",
								Aliases:  []string{"t"},
								Usage:    "Name of the MCP tool to associate.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "acl",
								Aliases:  []string{"a"},
								Usage:   "Access control level (e.g., EXECUTE, READ_ONLY, DENY).",
								Value:   "EXECUTE",
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolAssociate(c)
						},
					},
					{
						Name:  "disassociate",
						Usage: "Disassociate an MCP tool from a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "profile",
								Aliases:  []string{"p"},
								Usage:    "Name of the profile to disassociate the tool from.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "tool",
								Aliases:  []string{"t"},
								Usage:    "Name of the MCP tool to disassociate.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolDisassociate(c)
						},
					},
					{
						Name:  "list",
						Usage: "List tools associated with a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "profile",
								Aliases:  []string{"p"},
								Usage:    "Name of the profile to list tools for.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolList(c)
						},
					},
				},
			},
			{
				Name:  "user",
				Usage: "Manage users and their roles.",
				Subcommands: []*cli.Command{
					{
						Name:  "create",
						Usage: "Create a new user.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "username",
								Aliases:  []string{"u"},
								Usage:    "Username for the new user.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "password",
								Aliases:  []string{"p"},
								Usage:    "Password for the new user.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserCreate(c)
						},
					},
					{
						Name:  "list",
						Usage: "List all users.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserList(c)
						},
					},
					{
						Name:  "assign-role",
						Usage: "Assign a role to a user.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "username",
								Aliases:  []string{"u"},
								Usage:    "Username to assign the role to.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "role",
								Aliases:  []string{"r"},
								Usage:   "Name of the role to assign.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserAssignRole(c)
						},
					},
					{
						Name:  "remove-role",
						Usage: "Remove a role from a user.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "username",
								Aliases:  []string{"u"},
								Usage:    "Username to remove the role from.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "role",
								Aliases:  []string{"r"},
								Usage:   "Name of the role to remove.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserRemoveRole(c)
						},
					},
				},
			},
			{
				Name:  "role",
				Usage: "Manage roles and their profile access.",
				Subcommands: []*cli.Command{
					{
						Name:  "create",
						Usage: "Create a new role.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleCreate(c)
						},
					},
					{
						Name:  "list",
						Usage: "List all roles.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleList(c)
						},
					},
					{
						Name:  "get",
						Usage: "Get details of a specific role.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleGet(c)
						},
					},
					{
						Name:  "update",
						Usage: "Update a role.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Current name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "new-name",
								Usage:    "New name for the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleUpdate(c)
						},
					},
					{
						Name:  "delete",
						Usage: "Delete a role.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the role to delete.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleDelete(c)
						},
					},
					{
						Name:  "grant-profile-access",
						Usage: "Grant a role access to a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "role",
								Aliases:  []string{"r"},
								Usage:    "Name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "profile",
								Aliases:  []string{"p"},
								Usage:    "Name of the profile.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleGrantProfileAccess(c)
						},
					},
					{
						Name:  "revoke-profile-access",
						Usage: "Revoke a role's access to a profile.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "role",
								Aliases:  []string{"r"},
								Usage:    "Name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:     "profile",
								Aliases:  []string{"p"},
								Usage:    "Name of the profile.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleRevokeProfileAccess(c)
						},
					},
					{
						Name:  "list-profiles",
						Usage: "List profiles accessible by a role.",
						Flags: []cli.Flag{
							&cli.StringFlag{
								Name:     "name",
								Aliases:  []string{"n"},
								Usage:    "Name of the role.",
								Required: true,
							},
							&cli.StringFlag{
								Name:    "db",
								Usage:   "PostgreSQL connection string.",
								Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
								EnvVars: []string{"DATABASE_URL"},
							},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunRoleListProfiles(c)
						},
					},
				},
			},
			{
				Name:  "tui",
				Usage: "Launch the interactive Text User Interface.",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:    "db",
						Usage:   "PostgreSQL connection string.",
						Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
						EnvVars: []string{"DATABASE_URL"},
					},
				},
				Action: func(c *cli.Context) error {
					return cmd.RunTUI(c)
				},
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatal(err)
	}
}