package integration

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"testing"

	"github.com/ravan/suse-air/internal/db"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/urfave/cli/v2"



	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/tui"
)

// Helper to run CLI commands for testing
func runCLICommand(t *testing.T, args []string) (string, string, error) {
	var ( // Capture stdout and stderr
		outbuf, errbuf bytes.Buffer
	)

	// Save original stdout and stderr
	oldStdout := os.Stdout
	oldStderr := os.Stderr

	// Redirect stdout and stderr
	os.Stdout = io.MultiWriter(oldStdout, &outbuf).(*os.File)
	os.Stderr = io.MultiWriter(oldStderr, &errbuf).(*os.File)

	defer func() { // Restore original stdout and stderr
		os.Stdout = oldStdout
		os.Stderr = oldStderr
	}()

	app := &cli.App{
		Name:  "air",
		Usage: "A command-line tool for managing MCP tools and servers.",
		Commands: []*cli.Command{
			{
				Name:  "profile",
				Usage: "Manage profiles (groups of MCP tools).",
				Subcommands: []*cli.Command{
					{
						Name:  "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "description"},
							&cli.StringFlag{Name: "path-segment", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							// Call the actual run function from cmd/air/main.go
							return runProfileCreate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runProfileList(c)
						},
					},
					{
						Name: "get",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runProfileGet(c)
						},
					},
					{
						Name: "update",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "new-name"},
							&cli.StringFlag{Name: "new-description"},
							&cli.StringFlag{Name: "new-path-segment"},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runProfileUpdate(c)
						},
					},
					{
						Name: "delete",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runProfileDelete(c)
						},
					},
				},
			},
			{
				Name:  "tool",
				Usage: "Manage tool associations with profiles.",
				Subcommands: []*cli.Command{
					{
						Name: "associate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "tool", Required: true},
							&cli.StringFlag{Name: "acl"},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runToolAssociate(c)
						},
					},
					{
						Name: "disassociate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "tool", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runToolDisassociate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runToolList(c)
						},
					},
				},
			},
			{
				Name:  "user",
				Usage: "Manage users and their roles.",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "password", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runUserCreate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runUserList(c)
						},
					},
					{
						Name: "assign-role",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "role", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runUserAssignRole(c)
						},
					},
					{
						Name: "remove-role",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "role", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return runUserRemoveRole(c)
						},
					},
				},
			},
			{
				Name:  "tui",
				Usage: "Launch the interactive Text User Interface.",
				Flags: []cli.Flag{
					&cli.StringFlag{
						Name:    "db",
						Usage:   "PostgreSQL connection string.",
						Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
						EnvVars: []string{"DATABASE_URL"},
					},
				},
				Action: func(c *cli.Context) error {
					return runTUI(c)
				},
			},
		},
	}

	err := app.RunContext(context.Background(), append([]string{"air"}, args...))
	return outbuf.String(), errbuf.String(), err
}

func TestCLI_ProfileCommands(t *testing.T) {
	SkipIfNoDatabase(t)

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Test profile create
	out, errOut, err := runCLICommand(t, []string{"profile", "create", "--name", "test-profile", "--description", "A test profile", "--path-segment", "test-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile created successfully: ID=")
	assert.Contains(t, out, "Name=test-profile")
	assert.Contains(t, out, "PathSegment=test-path")

	// Test profile list
	out, errOut, err = runCLICommand(t, []string{"profile", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profiles:")
	assert.Contains(t, out, "ID: 1, Name: test-profile, Description: A test profile, PathSegment: test-path")

	// Test profile get
	out, errOut, err = runCLICommand(t, []string{"profile", "get", "--name", "test-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile Details:")
	assert.Contains(t, out, "Name: test-profile")
	assert.Contains(t, out, "Description: A test profile")
	assert.Contains(t, out, "PathSegment: test-path")

	// Test profile update
	out, errOut, err = runCLICommand(t, []string{"profile", "update", "--name", "test-profile", "--new-name", "updated-profile", "--new-description", "Updated description", "--new-path-segment", "updated-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile updated successfully: ID=")
	assert.Contains(t, out, "Name=updated-profile")
	assert.Contains(t, out, "PathSegment=updated-path")

	// Verify update with get
	out, errOut, err = runCLICommand(t, []string{"profile", "get", "--name", "updated-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile Details:")
	assert.Contains(t, out, "Name: updated-profile")
	assert.Contains(t, out, "Description: Updated description")
	assert.Contains(t, out, "PathSegment: updated-path")

	// Test profile delete
	out, errOut, err = runCLICommand(t, []string{"profile", "delete", "--name", "updated-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile updated-profile deleted successfully.")

	// Verify delete with list
	out, errOut, err = runCLICommand(t, []string{"profile", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "No profiles found.")
}

func TestCLI_ToolCommands(t *testing.T) {
	SkipIfNoDatabase(t)

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Create a profile and a tool for association
	out, errOut, err := runCLICommand(t, []string{"profile", "create", "--name", "tool-profile", "--path-segment", "tool-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)

	// Manually insert a tool for testing tool association
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	toolName := "test-tool-cli"
	toolDescription := "A tool for CLI testing"
	inputSchema := []byte(`{"type": "object"}`)
	createdTool, err := queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    toolName,
		Description: pgtype.Text{String: toolDescription, Valid: true},
		InputSchema: inputSchema,
	})
	require.NoError(t, err)

	// Test tool associate
	out, errOut, err = runCLICommand(t, []string{"tool", "associate", "--profile", "tool-profile", "--tool", toolName, "--acl", "EXECUTE", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tool %s associated with profile tool-profile with ACL EXECUTE successfully.", toolName))

	// Test tool list
	out, errOut, err = runCLICommand(t, []string{"tool", "list", "--profile", "tool-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tools for profile tool-profile:"))
	assert.Contains(t, out, fmt.Sprintf("ID: %d, Name: %s, Description: %s", createdTool.ID, toolName, toolDescription))

	// Test tool disassociate
	out, errOut, err = runCLICommand(t, []string{"tool", "disassociate", "--profile", "tool-profile", "--tool", toolName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tool %s disassociated from profile tool-profile successfully.", toolName))

	// Verify disassociation with tool list
	out, errOut, err = runCLICommand(t, []string{"tool", "list", "--profile", "tool-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "No tools found for profile tool-profile.")
}

func TestCLI_UserCommands(t *testing.T) {
	SkipIfNoDatabase(t)

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Test user create
	out, errOut, err := runCLICommand(t, []string{"user", "create", "--username", "testuser", "--password", "password123", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "User created successfully: ID=")
	assert.Contains(t, out, "Username=testuser")

	// Test user list
	out, errOut, err = runCLICommand(t, []string{"user", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Users:")
	assert.Contains(t, out, "ID: 1, Username: testuser")

	// Create a role for testing user-role association
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	roleName := "admin-role"
	_, err = queries.CreateRole(context.Background(), roleName)
	require.NoError(t, err)

	// Test user assign-role
	out, errOut, err = runCLICommand(t, []string{"user", "assign-role", "--username", "testuser", "--role", roleName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Role %s assigned to user testuser successfully.", roleName))

	// Test user remove-role
	out, errOut, err = runCLICommand(t, []string{"user", "remove-role", "--username", "testuser", "--role", roleName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Role %s removed from user %s successfully.", roleName))
}

func TestTUI_FullFlow(t *testing.T) {
	SkipIfNoDatabase(t)

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Create a pipe for simulating input and capturing output
	r, w, _ := os.Pipe()
	oldStdin := os.Stdin
	oldStdout := os.Stdout
	os.Stdin = r
	os.Stdout = w

	// Restore stdin and stdout after test
	defer func() {
		os.Stdin = oldStdin
		os.Stdout = oldStdout
	}()

	// Run the TUI in a goroutine
	done := make(chan struct{})
	go func() {
		defer close(done)
		app := &cli.App{
			Name:  "air",
			Usage: "A command-line tool for managing MCP tools and servers.",
			Commands: []*cli.Command{
				{
					Name:  "tui",
					Usage: "Launch the interactive Text User Interface.",
					Flags: []cli.Flag{
						&cli.StringFlag{
							Name:    "db",
							Usage:   "PostgreSQL connection string.",
							Value:   "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable",
							EnvVars: []string{"DATABASE_URL"},
						},
					},
					Action: func(c *cli.Context) error {
						return runTUI(c)
					},
				},
			},
		}
		_ = app.RunContext(context.Background(), []string{"air", "tui", "--db", dbConnStr})
	}()

	// Simulate user input and capture output
	var outputBuffer bytes.Buffer
	outputReader := io.TeeReader(r, &outputBuffer)

	// Helper to send keys
	sendKeys := func(keys ...string) {
		for _, k := range keys {
			_, err := r.Write([]byte(k))
			require.NoError(t, err)
			// Give some time for the TUI to process input
			time.Sleep(50 * time.Millisecond)
		}
	}

	// Helper to assert output
	assertOutput := func(expected string) {
		// Read until expected string is found or timeout
		timeout := time.After(2 * time.Second)
		for {
			select {
			case <-timeout:
				assert.Fail(t, "Timeout waiting for expected output", "Expected: %s\nActual: %s", expected, outputBuffer.String())
				return
			default:
				if strings.Contains(outputBuffer.String(), expected) {
					outputBuffer.Reset() // Clear buffer for next assertion
					return
				}
				time.Sleep(10 * time.Millisecond)
			}
		}
	}

	// --- Test Profile Management ---
	assertOutput("Welcome to SUSE AIR TUI!")
	sendKeys(string(tea.KeyEnter)) // Select "Profile Management"
	assertOutput("Profile Management")
	assertOutput("No profiles found. Press 'c' to create one.")

	sendKeys("c") // Create new profile
	assertOutput("Create New Profile:")
	sendKeys("test-tui-profile", string(tea.KeyTab), "A TUI created profile", string(tea.KeyTab), "tui-path", string(tea.KeyEnter))
	assertOutput("Profile 'test-tui-profile' created successfully!")
	assertOutput("test-tui-profile (Path: tui-path)")

	sendKeys(string(tea.KeyEnter)) // View details of test-tui-profile
	assertOutput("Profile Details for 'test-tui-profile':")
	assertOutput("Description: A TUI created profile")

	sendKeys(string(tea.KeyEsc)) // Go back to profile list
	assertOutput("Profiles:")

	// --- Test Tool Association (requires a tool to exist) ---
	// Manually insert a tool for testing tool association
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	toolName := "test-tui-tool"
	toolDescription := "A tool for TUI testing"
	inputSchema := []byte(`{"type": "object"}`)
	createdTool, err := queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    toolName,
		Description: pgtype.Text{String: toolDescription, Valid: true},
		InputSchema: inputSchema,
	})
	require.NoError(t, err)

	sendKeys(string(tea.KeyEsc)) // Go back to main menu
	assertOutput("Welcome to SUSE AIR TUI!")
	sendKeys(string(tea.KeyDown), string(tea.KeyEnter)) // Select "Tool Association"
	assertOutput("Tool Association Management")
	assertOutput("Select a Profile:")
	sendKeys(string(tea.KeyEnter)) // Select "test-tui-profile"
	assertOutput("Tools for Profile 'test-tui-profile':")
	assertOutput("No tools associated with this profile. Press 'a' to associate one.")

	sendKeys("a") // Associate tool
	assertOutput(fmt.Sprintf("Associate Tool '%s' with Profile 'test-tui-profile':", toolName))
	sendKeys("EXECUTE", string(tea.KeyEnter)) // Enter ACL and submit
	assertOutput(fmt.Sprintf("Tool '%s' associated with profile 'test-tui-profile' successfully!", toolName))
	assertOutput(fmt.Sprintf("ID: %d, Name: %s, Description: %s", createdTool.ID, toolName, toolDescription))

	sendKeys("d") // Disassociate tool
	assertOutput(fmt.Sprintf("Are you sure you want to disassociate tool '%s' from profile 'test-tui-profile'? (y/N)", toolName))
	sendKeys("y") // Confirm disassociation
	assertOutput(fmt.Sprintf("Tool '%s' disassociated from profile 'test-tui-profile' successfully!", toolName))
	assertOutput("No tools associated with this profile. Press 'a' to associate one.")

	// --- Test User Management ---
	sendKeys(string(tea.KeyEsc)) // Go back to profile list
	sendKeys(string(tea.KeyEsc)) // Go back to main menu
	assertOutput("Welcome to SUSE AIR TUI!")
	sendKeys(string(tea.KeyDown), string(tea.KeyDown), string(tea.KeyEnter)) // Select "User Management"
	assertOutput("User Management")
	assertOutput("No users found. Press 'c' to create one.")

	sendKeys("c") // Create new user
	assertOutput("Create New User:")
	sendKeys("tuiuser", string(tea.KeyTab), "tui-password", string(tea.KeyEnter))
	assertOutput("User 'tuiuser' created successfully!")
	assertOutput("ID: 1, Username: tuiuser")

	// Create a role for testing user-role association
	_, err = queries.CreateRole(context.Background(), "tui-role")
	require.NoError(t, err)

	sendKeys("a") // Assign role to user
	assertOutput("Assign Role to User 'tuiuser':")
	sendKeys("tui-role", string(tea.KeyEnter))
	assertOutput("Role tui-role assigned to user 'tuiuser' successfully!")

	sendKeys("r") // Remove role from user
	assertOutput("Are you sure you want to remove role from user 'tuiuser'? (y/N)")
	sendKeys("y") // Confirm removal
	assertOutput("Role tui-role removed from user 'tuiuser' successfully!")

	// Exit TUI
	sendKeys("q")
	<-done // Wait for TUI to exit
}