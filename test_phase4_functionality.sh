#!/bin/bash

# Test script for Phase 4 functionality
# This script tests the new CLI commands and TUI functionality

set -e

echo "=== Phase 4 Functionality Test ==="

# Build the application
echo "Building application..."
make build

# Set up test database connection
DB_URL="postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"

echo "Testing Role Management CLI Commands..."

# Test role creation
echo "1. Testing role creation..."
./sair role create --name "test-admin" --db "$DB_URL"
./sair role create --name "test-user" --db "$DB_URL"

# Test role listing
echo "2. Testing role listing..."
./sair role list --db "$DB_URL"

# Test role get
echo "3. Testing role get..."
./sair role get --name "test-admin" --db "$DB_URL"

# Test role update
echo "4. Testing role update..."
./sair role update --name "test-admin" --new-name "test-administrator" --db "$DB_URL"

# Test role listing after update
echo "5. Testing role listing after update..."
./sair role list --db "$DB_URL"

echo "Testing User Management CLI Commands..."

# Create a test user first
echo "6. Creating test user..."
./sair user create --username "testuser" --password "testpass" --db "$DB_URL"

# Test user get
echo "7. Testing user get..."
./sair user get --username "testuser" --db "$DB_URL"

# Test user update
echo "8. Testing user update..."
./sair user update --username "testuser" --password "newpass" --db "$DB_URL"

# Test user list roles (should be empty initially)
echo "9. Testing user list roles..."
./sair user list-roles --username "testuser" --db "$DB_URL"

# Test role assignment
echo "10. Testing role assignment..."
./sair user assign-role --username "testuser" --role "test-user" --db "$DB_URL"

# Test user list roles after assignment
echo "11. Testing user list roles after assignment..."
./sair user list-roles --username "testuser" --db "$DB_URL"

echo "Testing Profile-Role Association..."

# Create a test profile first
echo "12. Creating test profile..."
./sair profile create --name "test-profile" --description "Test profile" --path-segment "test" --db "$DB_URL"

# Test granting profile access to role
echo "13. Testing role grant profile access..."
./sair role grant-profile-access --role "test-user" --profile "test-profile" --db "$DB_URL"

# Test listing profiles accessible by role
echo "14. Testing role list profiles..."
./sair role list-profiles --name "test-user" --db "$DB_URL"

# Test revoking profile access from role
echo "15. Testing role revoke profile access..."
./sair role revoke-profile-access --role "test-user" --profile "test-profile" --db "$DB_URL"

# Test listing profiles after revoke
echo "16. Testing role list profiles after revoke..."
./sair role list-profiles --name "test-user" --db "$DB_URL"

echo "Cleanup..."

# Clean up test data
echo "17. Cleaning up test data..."
./sair user remove-role --username "testuser" --role "test-user" --db "$DB_URL" || true
./sair user delete --username "testuser" --db "$DB_URL" || true
./sair profile delete --name "test-profile" --db "$DB_URL" || true
./sair role delete --name "test-user" --db "$DB_URL" || true
./sair role delete --name "test-administrator" --db "$DB_URL" || true

echo "=== All Phase 4 CLI tests completed successfully! ==="

echo ""
echo "To test the TUI functionality, run:"
echo "  ./sair tui --db \"$DB_URL\""
echo ""
echo "In the TUI, you can:"
echo "  - Navigate to 'Role Management' to test role CRUD operations"
echo "  - Navigate to 'User Management' to test enhanced user operations"
echo "  - Test profile-role associations in the Role Management section"
